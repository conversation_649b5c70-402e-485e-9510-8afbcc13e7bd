package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.EventStatus;
import com.datalink.fdop.engine.mapper.EventStatsMapper;
import com.datalink.fdop.engine.service.EventStatsService;
import com.datalink.fdop.engine.utils.TrinoSqlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:33
 */
@Service
public class EventStatsServiceImpl extends ServiceImpl<EventStatsMapper, EventStatus> implements EventStatsService {

    @Autowired
    private EventStatsMapper eventStatsMapper;

    @Override
    public PageDataInfo<EventStatus> getOrg(String sort, SearchVo searchVo) {
        List<EventStatus> iPage = eventStatsMapper.getOrg(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public PageDataInfo<EventStatus> getEdit(String sort, SearchVo searchVo) {
        List<EventStatus> iPage = eventStatsMapper.getEdit(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public PageDataInfo<EventStatus> getAll(String sort, SearchVo searchVo) {
        List<EventStatus> iPage = eventStatsMapper.getAll(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public int saveData(EventStatus eventStatus) {
        if (eventStatsMapper.selectByKey(eventStatus) == null) {
            eventStatsMapper.add(TrinoSqlUtils.eventStatsSql(eventStatus));
        } else {
            eventStatsMapper.updateByKey(eventStatus);
        }
        return 1;
    }

    @Override
    public int deleteByList(List<EventStatus> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("请指定要删除的目标");
        }
        int i = 0;
        for (EventStatus eventStatus : list) {
            i = i + eventStatsMapper.deleteByKey(eventStatus);
        }
        return i;
    }

}
