package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewRbom;
import com.datalink.fdop.engine.mapper.DwsNewRbomMapper;
import com.datalink.fdop.engine.service.DwsNewRbomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewRbomServiceImpl extends ServiceImpl<DwsNewRbomMapper, DwsNewRbom> implements DwsNewRbomService {

    @Autowired
    private DwsNewRbomMapper dwsNewRbomMapper;

    @Override
    public PageDataInfo<DwsNewRbom> overview(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        Page<DwsNewRbom> page = PageUtils.getPage(DwsNewRbom.class);
        IPage<DwsNewRbom> iPage = dwsNewRbomMapper.selectAll(verId, plantId, productId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewRbom> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        return dwsNewRbomMapper.selectNoPage(verId, plantId, productId, sort, searchVo);
    }


}
