package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.datalink.fdop.base.api.domain.EventRules;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.engine.mapper.DataBalanceMapper;
import com.datalink.fdop.engine.mapper.PostgresMapper.EventRuleMapper;
import com.datalink.fdop.engine.model.vo.DataBalanceVo;
import com.datalink.fdop.engine.service.DataBalanceService;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-03-28 10:24
 */
@Service
public class DataBalanceServiceImpl implements DataBalanceService {

    @Autowired
    private DataBalanceMapper dataBalanceMapper;

    @Autowired
    private EventRuleMapper eventRuleMapper;

    @Override
    public Map<String, Object> dataBalance(String beginTime, String endTime) {
        Map<String, Object> result = Maps.newHashMap();
        List<String> columnName = new ArrayList<>();
        List<Map<String, Object>> headData = new ArrayList<>();
        List<Map<String, Object>> itemData = new ArrayList<>();

        Map<String, Integer> head = Maps.newHashMap();
        List<BigDecimal> balance = new ArrayList<>();
        Map<String, Integer> item = Maps.newHashMap();

        List<DataBalanceVo> headByProductStatusBOH = dataBalanceMapper.selectHeadByProductStatus(beginTime);
        List<DataBalanceVo> itemByProductStatusBOH = dataBalanceMapper.selectItemByProductStatus(beginTime);
        List<DataBalanceVo> headByProductStatusEOH = dataBalanceMapper.selectHeadByProductStatus(endTime);
        List<DataBalanceVo> itemByProductStatusEOH = dataBalanceMapper.selectItemByProductStatus(endTime);
        List<DataBalanceVo> headByEventStatus = dataBalanceMapper.selectHeadByEventStatus(beginTime, endTime);
        List<DataBalanceVo> itemByEventStatus = dataBalanceMapper.selectItemByEventStatus(beginTime, endTime);

        List<EventRules> list = eventRuleMapper.list();
        if (CollectionUtils.isNotEmpty(list)) {
            for (EventRules eventRules : list) {
                columnName.add(eventRules.getBalanceFactor());
                if ("BOH".equals(eventRules.getChangeFlag())) {
                    for (DataBalanceVo dataBalanceVo : headByProductStatusBOH) {
                        String key = dataBalanceVo.getFactoryId() + dataBalanceVo.getProductId() + dataBalanceVo.getLotType();
                        if (head.containsKey(key)) {
                            int index = head.get(key);
                            headData.get(index).put("BOH", dataBalanceVo.getQty());
                            if ("-".equals(eventRules.getCheckSign())) {
                                balance.set(index, balance.get(index).subtract(dataBalanceVo.getQty()));
                            } else {
                                balance.set(index, balance.get(index).add(dataBalanceVo.getQty()));
                            }
                        } else {
                            Map<String, Object> map = Maps.newHashMap();
                            map.put("FactoryId", dataBalanceVo.getFactoryId());
                            map.put("ProductId", dataBalanceVo.getProductId());
                            map.put("LotType", dataBalanceVo.getLotType());
                            map.put("BOH", dataBalanceVo.getQty());
                            headData.add(map);
                            head.put(key, head.size());
                            if ("-".equals(eventRules.getCheckSign())) {
                                balance.add(dataBalanceVo.getQty().negate());
                            } else {
                                balance.add(dataBalanceVo.getQty());
                            }
                        }
                    }
                    for (DataBalanceVo dataBalanceVo : itemByProductStatusBOH) {
                        String key = dataBalanceVo.getFactoryId() + dataBalanceVo.getProductId() + dataBalanceVo.getLotType() + dataBalanceVo.getLotId();
                        if (item.containsKey(key)) {
                            itemData.get(head.get(key)).put("BOH", dataBalanceVo.getQty());
                        } else {
                            Map<String, Object> map = Maps.newHashMap();
                            map.put("FactoryId", dataBalanceVo.getFactoryId());
                            map.put("ProductId", dataBalanceVo.getProductId());
                            map.put("LotType", dataBalanceVo.getLotType());
                            map.put("LotId", dataBalanceVo.getLotId());
                            map.put("BOH", dataBalanceVo.getQty());
                            itemData.add(map);
                            item.put(key, head.size());
                        }
                    }
                } else if ("EOH".equals(eventRules.getChangeFlag())){
                    for (DataBalanceVo dataBalanceVo : headByProductStatusEOH) {
                        String key = dataBalanceVo.getFactoryId() + dataBalanceVo.getProductId() + dataBalanceVo.getLotType();
                        if (head.containsKey(key)) {
                            int index = head.get(key);
                            headData.get(index).put("EOH", dataBalanceVo.getQty());
                            if ("-".equals(eventRules.getCheckSign())) {
                                balance.set(index, balance.get(index).subtract(dataBalanceVo.getQty()));
                            } else {
                                balance.set(index, balance.get(index).add(dataBalanceVo.getQty()));
                            }
                        } else {
                            Map<String, Object> map = Maps.newHashMap();
                            map.put("FactoryId", dataBalanceVo.getFactoryId());
                            map.put("ProductId", dataBalanceVo.getProductId());
                            map.put("LotType", dataBalanceVo.getLotType());
                            map.put("EOH", dataBalanceVo.getQty());
                            headData.add(map);
                            head.put(key, head.size());
                            if ("-".equals(eventRules.getCheckSign())) {
                                balance.add(dataBalanceVo.getQty().negate());
                            } else {
                                balance.add(dataBalanceVo.getQty());
                            }
                        }
                    }
                    for (DataBalanceVo dataBalanceVo : itemByProductStatusEOH) {
                        String key = dataBalanceVo.getFactoryId() + dataBalanceVo.getProductId() + dataBalanceVo.getLotType() + dataBalanceVo.getLotId();
                        if (item.containsKey(key)) {
                            itemData.get(head.get(key)).put("EOH", dataBalanceVo.getQty());
                        } else {
                            Map<String, Object> map = Maps.newHashMap();
                            map.put("FactoryId", dataBalanceVo.getFactoryId());
                            map.put("ProductId", dataBalanceVo.getProductId());
                            map.put("LotType", dataBalanceVo.getLotType());
                            map.put("LotId", dataBalanceVo.getLotId());
                            map.put("EOH", dataBalanceVo.getQty());
                            itemData.add(map);
                            item.put(key, head.size());
                        }
                    }
                } else {
                    for (DataBalanceVo dataBalanceVo : headByEventStatus) {
                        if (dataBalanceVo.getEventCode().equals(eventRules.getEventCode())) {
                            String key = dataBalanceVo.getFactoryId() + dataBalanceVo.getProductId() + dataBalanceVo.getLotType();
                            if (head.containsKey(key)) {
                                int index = head.get(key);
                                headData.get(index).put(dataBalanceVo.getEventCode(), dataBalanceVo.getQty());
                                if ("-".equals(eventRules.getCheckSign())) {
                                    balance.set(index, balance.get(index).subtract(dataBalanceVo.getQty()));
                                } else {
                                    balance.set(index, balance.get(index).add(dataBalanceVo.getQty()));
                                }
                            } else {
                                Map<String, Object> map = Maps.newHashMap();
                                map.put("FactoryId", dataBalanceVo.getFactoryId());
                                map.put("ProductId", dataBalanceVo.getProductId());
                                map.put("LotType", dataBalanceVo.getLotType());
                                map.put(dataBalanceVo.getEventCode(), dataBalanceVo.getQty());
                                headData.add(map);
                                head.put(key, head.size());
                                if ("-".equals(eventRules.getCheckSign())) {
                                    balance.add(dataBalanceVo.getQty().negate());
                                } else {
                                    balance.add(dataBalanceVo.getQty());
                                }
                            }
                        }
                    }
                    for (DataBalanceVo dataBalanceVo : itemByEventStatus) {
                        if (dataBalanceVo.getEventCode().equals(eventRules.getEventCode())) {
                            String key = dataBalanceVo.getFactoryId() + dataBalanceVo.getProductId() + dataBalanceVo.getLotType() + dataBalanceVo.getLotId();
                            if (item.containsKey(key)) {
                                itemData.get(head.get(key)).put(dataBalanceVo.getEventCode(), dataBalanceVo.getQty());
                            } else {
                                Map<String, Object> map = Maps.newHashMap();
                                map.put("FactoryId", dataBalanceVo.getFactoryId());
                                map.put("ProductId", dataBalanceVo.getProductId());
                                map.put("LotType", dataBalanceVo.getLotType());
                                map.put("LotId", dataBalanceVo.getLotId());
                                map.put(dataBalanceVo.getEventCode(), dataBalanceVo.getQty());
                                itemData.add(map);
                                item.put(key, head.size());
                            }
                        }
                    }
                }
            }
        }

        if (balance.size() != 0) {
            for (int i = 0; i < balance.size(); i++) {
                if (balance.get(i).equals(BigDecimal.ZERO)) {
                    headData.get(i).put("balance", "Y");
                } else {
                    headData.get(i).put("balance", "N");
                }
            }
        }

        result.put("columnName", columnName);
        result.put("headData", PageUtils.getPageInfo(headData, headData.size()));
        result.put("itemData", PageUtils.getPageInfo(itemData, itemData.size()));
        return result;
    }

}
