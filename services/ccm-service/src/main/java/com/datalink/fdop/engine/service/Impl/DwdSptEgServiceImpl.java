package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.base.api.domain.DwdSptEg;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.mapper.DwdSptEgMapper;
import com.datalink.fdop.engine.service.DwdSptEgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdSptEgServiceImpl extends ServiceImpl<DwdSptEgMapper, DwdSptEg> implements DwdSptEgService {

    @Autowired
    private DwdSptEgMapper dwdSptEgMapper;

    @Override
    public PageDataInfo<DwdSptEg> overview(String plantId, String equipGroupId, String sort, SearchVo searchVo) {
        IPage<DwdSptEg> iPage = dwdSptEgMapper.selectAll(plantId, equipGroupId, PageUtils.getPage(DwdSptEg.class), sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdSptEg> selectNoPage(String plantId, String equipGroupId, String sort, SearchVo searchVo) {
        return dwdSptEgMapper.selectNoPage(plantId, equipGroupId, sort, searchVo);
    }
}