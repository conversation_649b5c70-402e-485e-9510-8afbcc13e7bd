package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhTerpFlow;
import com.datalink.fdop.engine.mapper.DwhTerpFlowMapper;
import com.datalink.fdop.engine.service.DwhTerpFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhTerpFlowServiceImpl extends ServiceImpl<DwhTerpFlowMapper, DwhTerpFlow> implements DwhTerpFlowService {

    @Autowired
    private DwhTerpFlowMapper dwhTerpFlowMapper;

    @Override
    public PageDataInfo<DwhTerpFlow> overview(String verId,String  plantId,String productId,String sort, SearchVo searchVo) {
        Page<DwhTerpFlow> page = PageUtils.getPage(DwhTerpFlow.class);
        IPage<DwhTerpFlow> iPage = dwhTerpFlowMapper.selectAll(verId,  plantId, productId,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhTerpFlow> selectNoPage(String verId, String plantId, String productId, SearchVo searchVo) {
        return dwhTerpFlowMapper.selectNoPage(verId, plantId, productId, searchVo);
    }


}
