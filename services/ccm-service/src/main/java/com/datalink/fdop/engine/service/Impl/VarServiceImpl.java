package com.datalink.fdop.engine.service.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.drive.api.RemoteTaskService;
import com.datalink.fdop.engine.api.domain.*;
import com.datalink.fdop.engine.mapper.VarMapper;
import com.datalink.fdop.engine.service.VarService;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-03-01 17:11
 */
@Service
public class VarServiceImpl implements VarService {

    private static final Logger log = LoggerFactory.getLogger(VarServiceImpl.class);

    private static final String PARAM = "var_param";

    @Autowired
    private RemoteTaskService remoteTaskService;


    @Autowired
    private VarMapper varMapper;

    @Override
    public Map<String, Object> ppv(String verId, String companyId, Long year, Long month, String businessType, SearchVo searchVo, String str) {
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> dynamicColumns = Maps.newLinkedHashMap();
        if (varMapper.selectVarPpvHead(verId, companyId, year, month, searchVo, "s2", "a").size() != 0) {
            result.put("state", 3);
        } else if (varMapper.selectVarPpvHead(verId, companyId, year, month, searchVo, "s1", "a").size() != 0){
            result.put("state", 2);
        } else {
            result.put("state", 1);
        }
        List<VarPpvHead> varPpvHeadList = varMapper.selectVarPpvHead(verId, companyId, year, month, searchVo, str, null);
        for (VarPpvHead varPpvHead : varPpvHeadList) {
            List<VarPpvItem> varPpvItemList = varMapper.selectVarPpvItem(varPpvHead, str);
            for (VarPpvItem varPpvItem : varPpvItemList) {
                varPpvHead.getFlowTo().put(varPpvItem.getFactoryFlowTo(), varPpvItem.getVarF());
                if (!dynamicColumns.containsKey(varPpvItem.getFactoryFlowTo())) {
                    dynamicColumns.put(varPpvItem.getFactoryFlowTo() , varPpvItem.getVarF());
                }
            }
        }
        result.put("data", PageUtils.getPageInfo(varPpvHeadList, varPpvHeadList.size()));
        result.put("dynamicColumns", dynamicColumns);
        return result;
    }

    @Override
    public Map<String, Object> mpv(String verId, String companyId, Long year, Long month, String businessType, SearchVo searchVo, String str) {
        Map<String, Object> result = Maps.newHashMap();
        if (varMapper.selectVarMpv(verId, companyId, year, month, searchVo, "s2", "a").size() != 0) {
            result.put("state", 3);
        } else if (varMapper.selectVarMpv(verId, companyId, year, month, searchVo, "s1", "a").size() != 0){
            result.put("state", 2);
        } else {
            result.put("state", 1);
        }
        List<VarMpv> varMpvHeadList = varMapper.selectVarMpv(verId, companyId, year, month, searchVo, str, null);
        result.put("data", PageUtils.getPageInfo(varMpvHeadList, varMpvHeadList.size()));
        return result;
    }

    @Override
    public Map<String, Object> posting(String verId, String companyId, Long year, Long month, String businessType, SearchVo searchVo, String str) {
        Map<String, Object> result = Maps.newHashMap();
        PostingLogHead postingLogHead = varMapper.selectPostingLogHead(businessType, verId, companyId, year, month, str);
        if (postingLogHead != null) {
            List<PostingLogItem> postingLogItemList = varMapper.selectPostingLogItem(businessType, verId, postingLogHead.getFactoryId(), year, month, companyId, postingLogHead.getDocumentCode(), searchVo, str);
            result.put("postingLogItem", PageUtils.getPageInfo(postingLogItemList, postingLogItemList.size()));
        } else {
            result.put("postingLogItem", PageUtils.getPageInfo(new ArrayList<>(), 0));
        }
        result.put("postingLogHead", postingLogHead == null ? new PostingLogHead() : postingLogHead);
        return result;
    }

    @Override
    public int varSave(String verId, String companyId, Long year, Long month, String businessType, String str) {
        String tableName = businessType.toLowerCase(Locale.ROOT);
        if ("ppv".equals(tableName)) {
            if (varMapper.selectVarPpvHead(verId, companyId, year, month, null, str, "a").size() != 0) {
                throw new ServiceException("该情况已经执行过该操作！");
            }
        } else if ("mpv".equals(tableName)) {
            if (varMapper.selectVarMpv(verId, companyId, year, month, null, str, "a").size() != 0) {
                throw new ServiceException("该情况已经执行过该操作！");
            }
        }
        String str1 = "";
        if ("s1".equals(str)) {
            str1 = "temp";
        } else {
            str1 = "s1";
        }
        varMapper.varSaveHead(verId, companyId, year, month, tableName, str, str1);
        if ("PPV".equals(businessType)) {
            varMapper.varSaveItem(verId, companyId, year, month, str, str1);
        }
        varMapper.postingSaveHead(verId, companyId, year, month, businessType, str, str1);
        varMapper.postingSaveItem(verId, companyId, year, month, businessType, str, str1);
//        Long projectCode = getProjectCode(remoteProjectService.queryProjectByName(SecurityUtils.getTenantId().toString()));
//        Map<String, Object> map = Maps.newHashMap();
//        map.put("verId", verId);
//        map.put("companyId", companyId);
//        map.put("year", year);
//        map.put("month", month);
//        map.put("businessType", businessType);
//        if ("s1".equals(str)) {
//            map.put("from", "temp");
//            map.put("to", str);
//        } else {
//            map.put("from", "s1");
//            map.put("to", str);
//        }
//        String taskName = "var_" + businessType.toLowerCase(Locale.ROOT);
//        Long taskCode = varMapper.selectTaskCodeByName(taskName);
//        if (taskCode == null) {
//            log.info("{} 任务不存在", taskName);
//        } else {
//            R<GlobalParam> paramR = remoteParamService.selectByCode(PARAM);
//            if (paramR.getCode() != 200) {
//                log.info("请检查全局参数服务是否启动");
//            } else {
//                GlobalParam param = paramR.getData();
//                if (param == null) {
//                    log.info("{} 参数不存在", PARAM);
//                } else {
//                    R r = remoteParamService.updateParamValue(param.getId(), map);
//                    if (r.getCode() != 200) {
//                        log.info("请检查全局参数服务是否启动");
//                    } else {
//                        R r1 = remoteTaskService.runTask(projectCode, taskCode);
//                        if (r1.getCode() != 200) {
//                            log.info("请检查调度服务是否启动");
//                        }
//                        this.savePosting(projectCode);
//                    }
//                }
//            }
//        }

        return 0;
    }

    @Override
    public int varUndo(String verId, String companyId, Long year, Long month, String businessType, String str) {
        String tableName = businessType.toLowerCase(Locale.ROOT);
        if ("ppv".equals(tableName)) {
            if (varMapper.selectVarPpvHead(verId, companyId, year, month, null, str, "a").size() == 0) {
                throw new ServiceException("该情况未执行过该操作！无须执行撤销操作！");
            }
        } else if ("mpv".equals(tableName)) {
            if (varMapper.selectVarMpv(verId, companyId, year, month, null, str, "a").size() == 0) {
                throw new ServiceException("该情况未执行过该操作！无须执行撤销操作！");
            }
        }
        varMapper.varDeleteHead(verId, companyId, year, month, tableName, str);
        if ("PPV".equals(businessType)) {
            varMapper.varDeleteItem(verId, companyId, year, month, str);
        }
        varMapper.postingDeleteHead(verId, companyId, year, month, businessType, str);
        varMapper.postingDeleteItem(verId, companyId, year, month, businessType, str);
        return 0;
    }

    private Long getProjectCode(R data) {
        JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(data.getData()));
        return jsonObject.getLong("code");
    }

    private void savePosting(Long projectCode) {
        String taskName = "var_posting";
        Long taskCode = varMapper.selectTaskCodeByName(taskName);
        if (taskCode == null) {
            log.info("{} 任务不存在", taskName);
        } else {
            R r = remoteTaskService.runTask(projectCode, taskCode);
            if (r.getCode() != 200) {
                log.info("请检查调度服务是否启动");
            }
        }
    }

}
