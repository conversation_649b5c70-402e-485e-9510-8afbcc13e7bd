package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdTraceExp;
import com.datalink.fdop.engine.mapper.DwdTraceExpMapper;
import com.datalink.fdop.engine.service.DwdTraceExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdTraceExpServiceImpl extends ServiceImpl<DwdTraceExpMapper, DwdTraceExp> implements DwdTraceExpService {

    @Autowired
    private DwdTraceExpMapper dwdTraceExpMapper;

    @Override
    public PageDataInfo<DwdTraceExp> overview(String yearMonth, String controlAreaId, String companyId, String sort, SearchVo searchVo) {
        Page<DwdTraceExp> page = PageUtils.getPage(DwdTraceExp.class);
        IPage<DwdTraceExp> iPage = dwdTraceExpMapper.selectAll(yearMonth, controlAreaId, companyId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdTraceExp> selectNoPage(String yearMonth, String controlAreaId, String companyId, String sort, SearchVo searchVo) {
        return dwdTraceExpMapper.selectNoPage(yearMonth, controlAreaId, companyId, sort, searchVo);
    }
}