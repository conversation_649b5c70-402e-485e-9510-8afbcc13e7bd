package com.datalink.fdop.engine.service.Impl;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.*;
import com.datalink.fdop.engine.mapper.DrillDowntMapper;
import com.datalink.fdop.engine.service.DrillDowntService;
import io.seata.common.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-16 16:43
 */
@Service
public class DrillDowntServiceImpl implements DrillDowntService {

    @Autowired
    private DrillDowntMapper drillDowntMapper;


    @Override
    public PageDataInfo<DrillDowntReport100> query1(String verId, String companyId, String factoryId, Long yearMonth, String level, String var1, String var2, String sort, SearchVo searchVo) {
        List<DrillDowntReport100> drillDowntReport100List = drillDowntMapper.queryHead1(verId, companyId, factoryId, yearMonth, sort, searchVo);
        if (CollectionUtils.isNotEmpty(drillDowntReport100List)) {
            List<DrillDowntReport200> drillDowntReport200List = drillDowntMapper.queryItem1(verId, companyId, factoryId, yearMonth, level);
            for (DrillDowntReport100 drillDowntReport100 : drillDowntReport100List) {
                if ("1".equals(var1) && "2".equals(var2)) {
                    drillDowntReport100.setVarCost(drillDowntReport100.getCost1().subtract(drillDowntReport100.getCost2()));
                } else if ("1".equals(var1) && "3".equals(var2)) {
                    drillDowntReport100.setVarCost(drillDowntReport100.getCost1().subtract(drillDowntReport100.getCost3()));
                } else if ("2".equals(var1) && "3".equals(var2)) {
                    drillDowntReport100.setVarCost(drillDowntReport100.getCost2().subtract(drillDowntReport100.getCost3()));
                }
                drillDowntReport100.setVarUnit1();
                drillDowntReport100.setVarUnit2();
                if (CollectionUtils.isNotEmpty(drillDowntReport200List)) {
                    for (DrillDowntReport200 drillDowntReport200 : drillDowntReport200List) {
                        if (drillDowntReport200.getCostCenterId().equals(drillDowntReport100.getCostCenterId())) {
                            if ("1".equals(var1) && "2".equals(var2)) {
                                drillDowntReport200.setVarCost(drillDowntReport200.getCost1().subtract(drillDowntReport200.getCost2()));
                            } else if ("1".equals(var1) && "3".equals(var2)) {
                                drillDowntReport200.setVarCost(drillDowntReport200.getCost1().subtract(drillDowntReport200.getCost3()));
                            } else if ("2".equals(var1) && "3".equals(var2)) {
                                drillDowntReport200.setVarCost(drillDowntReport200.getCost2().subtract(drillDowntReport200.getCost3()));
                            }
                            drillDowntReport200.setVar();
                            drillDowntReport100.getItems().add(drillDowntReport200);
                        }
                    }
                }
            }
        }
        return PageUtils.getPageInfo(drillDowntReport100List, drillDowntReport100List.size());
    }

    @Override
    public PageDataInfo<DrillDowntReport110> query2(String verId, String companyId, String factoryId, Long yearMonth, String level, String var1, String var2, String sort, SearchVo searchVo) {
        List<DrillDowntReport110> drillDowntReport110List = drillDowntMapper.queryHead2(verId, companyId, factoryId, yearMonth, sort, searchVo);
        if (CollectionUtils.isNotEmpty(drillDowntReport110List)) {
            List<DrillDowntReport210> drillDowntReport210List = drillDowntMapper.queryItem2(verId, companyId, factoryId, yearMonth, level);
            for (DrillDowntReport110 drillDowntReport110 : drillDowntReport110List) {
                if ("1".equals(var1) && "2".equals(var2)) {
                    drillDowntReport110.setVarCost(drillDowntReport110.getCost1().subtract(drillDowntReport110.getCost2()));
                } else if ("1".equals(var1) && "3".equals(var2)) {
                    drillDowntReport110.setVarCost(drillDowntReport110.getCost1().subtract(drillDowntReport110.getCost3()));
                } else if ("2".equals(var1) && "3".equals(var2)) {
                    drillDowntReport110.setVarCost(drillDowntReport110.getCost2().subtract(drillDowntReport110.getCost3()));
                }
                drillDowntReport110.setVarUnit1();
                drillDowntReport110.setVarUnit2();
                if (CollectionUtils.isNotEmpty(drillDowntReport210List)) {
                    for (DrillDowntReport210 drillDowntReport210 : drillDowntReport210List) {
                        if (drillDowntReport210.getCostCenterId().equals(drillDowntReport110.getCostCenterId())) {
                            if ("1".equals(var1) && "2".equals(var2)) {
                                drillDowntReport210.setVarCost(drillDowntReport210.getCost1().subtract(drillDowntReport210.getCost2()));
                            } else if ("1".equals(var1) && "3".equals(var2)) {
                                drillDowntReport210.setVarCost(drillDowntReport210.getCost1().subtract(drillDowntReport210.getCost3()));
                            } else if ("2".equals(var1) && "3".equals(var2)) {
                                drillDowntReport210.setVarCost(drillDowntReport210.getCost2().subtract(drillDowntReport210.getCost3()));
                            }
                            drillDowntReport210.setVar();
                            drillDowntReport110.getItems().add(drillDowntReport210);
                        }
                    }
                }
            }
        }
        return PageUtils.getPageInfo(drillDowntReport110List, drillDowntReport110List.size());
    }

    @Override
    public PageDataInfo<DrillDowntReport120> query3(String verId, String companyId, String factoryId, Long yearMonth, String level, String var1, String var2, String sort, SearchVo searchVo) {
        List<DrillDowntReport120> drillDowntReport120List = drillDowntMapper.queryHead3(verId, companyId, factoryId, yearMonth, sort, searchVo);
        if (CollectionUtils.isNotEmpty(drillDowntReport120List)) {
            List<DrillDowntReport220> drillDowntReport220List = drillDowntMapper.queryItem3(verId, companyId, factoryId, yearMonth, level);
            for (DrillDowntReport120 drillDowntReport120 : drillDowntReport120List) {
                if ("1".equals(var1) && "2".equals(var2)) {
                    drillDowntReport120.setVarCost(drillDowntReport120.getCost1().subtract(drillDowntReport120.getCost2()));
                } else if ("1".equals(var1) && "3".equals(var2)) {
                    drillDowntReport120.setVarCost(drillDowntReport120.getCost1().subtract(drillDowntReport120.getCost3()));
                } else if ("2".equals(var1) && "3".equals(var2)) {
                    drillDowntReport120.setVarCost(drillDowntReport120.getCost2().subtract(drillDowntReport120.getCost3()));
                }
                drillDowntReport120.setVarUnit1();
                drillDowntReport120.setVarUnit2();
                if (CollectionUtils.isNotEmpty(drillDowntReport220List)) {
                    for (DrillDowntReport220 drillDowntReport220 : drillDowntReport220List) {
                        if (drillDowntReport220.getCostCenterId().equals(drillDowntReport120.getCostCenterId())) {
                            if ("1".equals(var1) && "2".equals(var2)) {
                                drillDowntReport220.setVarCost(drillDowntReport220.getCost1().subtract(drillDowntReport220.getCost2()));
                            } else if ("1".equals(var1) && "3".equals(var2)) {
                                drillDowntReport220.setVarCost(drillDowntReport220.getCost1().subtract(drillDowntReport220.getCost3()));
                            } else if ("2".equals(var1) && "3".equals(var2)) {
                                drillDowntReport220.setVarCost(drillDowntReport220.getCost2().subtract(drillDowntReport220.getCost3()));
                            }
                            drillDowntReport220.setVar();
                            drillDowntReport120.getItems().add(drillDowntReport220);
                        }
                    }
                }
            }
        }
        return PageUtils.getPageInfo(drillDowntReport120List, drillDowntReport120List.size());
    }

    @Override
    public PageDataInfo<DrillDowntReport130> query4(String verId, String companyId, String factoryId, Long yearMonth, String level, String var1, String var2, String sort, SearchVo searchVo) {
        List<DrillDowntReport130> drillDowntReport130List = drillDowntMapper.queryHead4(verId, companyId, factoryId, yearMonth, sort, searchVo);
        if (CollectionUtils.isNotEmpty(drillDowntReport130List)) {
            List<DrillDowntReport230> drillDowntReport230List = drillDowntMapper.queryItem4(verId, companyId, factoryId, yearMonth, level);
            for (DrillDowntReport130 drillDowntReport130 : drillDowntReport130List) {
                if ("1".equals(var1) && "2".equals(var2)) {
                    drillDowntReport130.setVarCost(drillDowntReport130.getCost1().subtract(drillDowntReport130.getCost2()));
                } else if ("1".equals(var1) && "3".equals(var2)) {
                    drillDowntReport130.setVarCost(drillDowntReport130.getCost1().subtract(drillDowntReport130.getCost3()));
                } else if ("2".equals(var1) && "3".equals(var2)) {
                    drillDowntReport130.setVarCost(drillDowntReport130.getCost2().subtract(drillDowntReport130.getCost3()));
                }
                drillDowntReport130.setVarUnit1();
                drillDowntReport130.setVarUnit2();
                if (CollectionUtils.isNotEmpty(drillDowntReport230List)) {
                    for (DrillDowntReport230 drillDowntReport230 : drillDowntReport230List) {
                        if (drillDowntReport230.getCostCenterId().equals(drillDowntReport130.getCostCenterId())) {
                            if ("1".equals(var1) && "2".equals(var2)) {
                                drillDowntReport230.setVarCost(drillDowntReport230.getCost1().subtract(drillDowntReport230.getCost2()));
                            } else if ("1".equals(var1) && "3".equals(var2)) {
                                drillDowntReport230.setVarCost(drillDowntReport230.getCost1().subtract(drillDowntReport230.getCost3()));
                            } else if ("2".equals(var1) && "3".equals(var2)) {
                                drillDowntReport230.setVarCost(drillDowntReport230.getCost2().subtract(drillDowntReport230.getCost3()));
                            }
                            drillDowntReport230.setVar();
                            drillDowntReport130.getItems().add(drillDowntReport230);
                        }
                    }
                }
            }
        }
        return PageUtils.getPageInfo(drillDowntReport130List, drillDowntReport130List.size());
    }

    @Override
    public PageDataInfo<DrillDowntReport140> query5(String verId, String companyId, String factoryId, Long yearMonth, String level, String var1, String var2, String sort, SearchVo searchVo) {
        List<DrillDowntReport140> drillDowntReport140List = drillDowntMapper.queryHead5(verId, companyId, factoryId, yearMonth, sort, searchVo);
        if (CollectionUtils.isNotEmpty(drillDowntReport140List)) {
            List<DrillDowntReport240> drillDowntReport240List = drillDowntMapper.queryItem5(verId, companyId, factoryId, yearMonth, level);
            for (DrillDowntReport140 drillDowntReport140 : drillDowntReport140List) {
                if ("1".equals(var1) && "2".equals(var2)) {
                    drillDowntReport140.setVarCost(drillDowntReport140.getCost1().subtract(drillDowntReport140.getCost2()));
                } else if ("1".equals(var1) && "3".equals(var2)) {
                    drillDowntReport140.setVarCost(drillDowntReport140.getCost1().subtract(drillDowntReport140.getCost3()));
                } else if ("2".equals(var1) && "3".equals(var2)) {
                    drillDowntReport140.setVarCost(drillDowntReport140.getCost2().subtract(drillDowntReport140.getCost3()));
                }
                drillDowntReport140.setVarUnit1();
                drillDowntReport140.setVarUnit2();
                if (CollectionUtils.isNotEmpty(drillDowntReport240List)) {
                    for (DrillDowntReport240 drillDowntReport240 : drillDowntReport240List) {
                        if (drillDowntReport240.getCostCenterId().equals(drillDowntReport140.getCostCenterId())) {
                            if ("1".equals(var1) && "2".equals(var2)) {
                                drillDowntReport240.setVarCost(drillDowntReport240.getCost1().subtract(drillDowntReport240.getCost2()));
                            } else if ("1".equals(var1) && "3".equals(var2)) {
                                drillDowntReport240.setVarCost(drillDowntReport240.getCost1().subtract(drillDowntReport240.getCost3()));
                            } else if ("2".equals(var1) && "3".equals(var2)) {
                                drillDowntReport240.setVarCost(drillDowntReport240.getCost2().subtract(drillDowntReport240.getCost3()));
                            }
                            drillDowntReport240.setVar();
                            drillDowntReport140.getItems().add(drillDowntReport240);
                        }
                    }
                }
            }
        }
        return PageUtils.getPageInfo(drillDowntReport140List, drillDowntReport140List.size());
    }
}
