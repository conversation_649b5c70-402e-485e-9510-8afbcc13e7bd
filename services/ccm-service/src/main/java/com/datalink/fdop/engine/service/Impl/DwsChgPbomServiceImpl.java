package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsChgPbom;
import com.datalink.fdop.engine.mapper.DwsChgPbomMapper;
import com.datalink.fdop.engine.service.DwsChgPbomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsChgPbomServiceImpl extends ServiceImpl<DwsChgPbomMapper, DwsChgPbom> implements DwsChgPbomService {

    @Autowired
    private DwsChgPbomMapper dwsChgPbomMapper;

    @Override
    public PageDataInfo<DwsChgPbom> overview(String verId,String  plantId,String productId,String sort, SearchVo searchVo) {
        Page<DwsChgPbom> page = PageUtils.getPage(DwsChgPbom.class);
        IPage<DwsChgPbom> iPage = dwsChgPbomMapper.selectAll(verId,  plantId, productId,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsChgPbom> listAll() {
        return dwsChgPbomMapper.listAll();
    }

    @Override
    public List<DwsChgPbom> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        return dwsChgPbomMapper.selectNoPage(verId, plantId, productId, sort, searchVo);
    }

}
