package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.base.api.domain.DwhFinalTraceAct;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.mapper.DwhFinalTraceActMapper;
import com.datalink.fdop.engine.service.DwhFinalTraceActService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalTraceActServiceImpl extends ServiceImpl<DwhFinalTraceActMapper, DwhFinalTraceAct> implements DwhFinalTraceActService {

    @Autowired
    private DwhFinalTraceActMapper dwhFinalTraceActMapper;

    @Override
    public PageDataInfo<DwhFinalTraceAct> overview(String workOrder, String plantId,String productId,
                                                   String  dateFrom,String  dateTo,
                                                   String sort, SearchVo searchVo) {
        Page<DwhFinalTraceAct> page = PageUtils.getPage(DwhFinalTraceAct.class);
        IPage<DwhFinalTraceAct> iPage = dwhFinalTraceActMapper.selectAll(workOrder, plantId,productId,dateFrom,dateTo,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalTraceAct> selectNoPage(String workOrder, String plantId, String productId, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        return dwhFinalTraceActMapper.selectNoPage(workOrder, plantId, productId, dateFrom, dateTo, sort, searchVo);
    }


}
