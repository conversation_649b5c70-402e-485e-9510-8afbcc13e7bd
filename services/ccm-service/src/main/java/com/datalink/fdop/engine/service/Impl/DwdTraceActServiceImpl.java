package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.base.api.domain.DwdTraceAct;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.mapper.DwdTraceActMapper;
import com.datalink.fdop.engine.service.DwdTraceActService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdTraceActServiceImpl extends ServiceImpl<DwdTraceActMapper, DwdTraceAct> implements DwdTraceActService {

    @Autowired
    private DwdTraceActMapper dwdTraceActMapper;

    @Override
    public PageDataInfo<DwdTraceAct> overview(String verId, String plantId,String productId,
                                              String  dateFrom,String  dateTo,String sort, SearchVo searchVo) {
        Page<DwdTraceAct> page = PageUtils.getPage(DwdTraceAct.class);
        IPage<DwdTraceAct> iPage = dwdTraceActMapper.selectAll(verId, plantId, productId, dateFrom, dateTo,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdTraceAct> selectNoPage(String verId, String plantId, String productId, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        return dwdTraceActMapper.selectNoPage(verId, plantId, productId, dateFrom, dateTo, sort, searchVo);
    }


}
