package com.datalink.fdop.engine.service.Impl;

import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.PostingLogHead2;
import com.datalink.fdop.engine.api.domain.PostingLogItem2;
import com.datalink.fdop.engine.mapper.PostingLogMapper;
import com.datalink.fdop.engine.model.vo.PostingLog;
import com.datalink.fdop.engine.service.PostingLogService;
import com.datalink.fdop.engine.utils.DocumentIdUtils;
import com.datalink.fdop.engine.utils.TrinoSqlUtils;
import io.seata.common.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-14 14:55
 */
@Service
public class PostingLogServiceImpl implements PostingLogService {

    @Autowired
    private PostingLogMapper postingLogMapper;

    @Override
    public PageDataInfo<PostingLog> query(String verId, String companyId, String actionFlag, String postingDate, String sort, SearchVo searchVo) {
        List<PostingLogHead2> postingLogHead2List = postingLogMapper.queryHead(verId, companyId, actionFlag, postingDate, sort);
        List<PostingLogItem2> postingLogItem2List = postingLogMapper.queryItem(searchVo, sort);
        List<PostingLog> postingLogList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(postingLogHead2List)) {
            for (PostingLogHead2 postingLogHead2 : postingLogHead2List) {
                PostingLog postingLog = new PostingLog();
                postingLog.setHead(postingLogHead2);
                for (PostingLogItem2 postingLogItem2 : postingLogItem2List) {
                    if (postingLogHead2.getDocumentId().equals(postingLogItem2.getDocumentId())) {
                        postingLog.getItems().add(postingLogItem2);
                    }
                }
                postingLogList.add(postingLog);
            }
        }
        return PageUtils.getPageInfo(postingLogList, postingLogList.size());
    }

    @Override
    public int add(PostingLog postingLog) {
        if (postingLog.getHead() == null) {
            return 0;
        } else {
            PostingLogHead2 head = postingLog.getHead();
            Long prefix = head.getYear()%100*100 + head.getMonth();
            Long documentId = postingLogMapper.selectDocumentId(prefix*100000000, (prefix+1)*100000000);
            if (documentId == null) {
                documentId = DocumentIdUtils.createDocumentId(head.getYear()%100, head.getMonth());
            } else {
                documentId = documentId+1;
            }
            head.setDocumentId(documentId);
            int i = postingLogMapper.addHead(TrinoSqlUtils.postingLogHeadSql(head));
            if (postingLog.getItems().size() != 0) {
                for (PostingLogItem2 postingLogItem2 : postingLog.getItems()) {
                    postingLogItem2.setDocumentId(documentId);
                    postingLogItem2.setPostingItemCode((long) i++);
                    postingLogMapper.addItem(TrinoSqlUtils.postingLogItemSql(postingLogItem2));
                }
            }
            return 1;
        }
    }

    @Override
    public int addItem(PostingLogItem2 postingLogItem2) {
        Long postingItemCode = postingLogMapper.selectPostingItemCode(postingLogItem2.getDocumentId());
        if (postingItemCode == null) {
            postingLogItem2.setPostingItemCode(1L);
        } else {
            postingLogItem2.setPostingItemCode(postingItemCode+1);
        }
        return postingLogMapper.addItem(TrinoSqlUtils.postingLogItemSql(postingLogItem2));
    }

    @Override
    public int updateHead(PostingLogHead2 postingLogHead2) {
        if (postingLogHead2.getDocumentId() == null) {
            throw new ServiceException("请指定要修改的数据");
        }
        return postingLogMapper.updateHead(postingLogHead2);
    }

    @Override
    public int updateItem(PostingLogItem2 postingLogItem2) {
        if (postingLogItem2.getDocumentId() == null || postingLogItem2.getPostingItemCode() == null) {
            throw new ServiceException("请指定要修改的数据");
        }
        return postingLogMapper.updateItem(postingLogItem2);
    }

    @Override
    public int delItem(List<PostingLogItem2> list) {
        int i = 0;
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("请指定要删除的数据");
        } else {
            for (PostingLogItem2 postingLogItem2 : list) {
                i = i + postingLogMapper.delItem(postingLogItem2);
            }
        }
        return i;
    }

    @Override
    public int updateStatus(Long id, String actionFlag) {
        return postingLogMapper.updateStatus(id, actionFlag);
    }

    @Override
    public int undo(Long id, String actionFlag) {
        if ("1A".equals(actionFlag)) {
            actionFlag = "2A";
        } else if ("1B".equals(actionFlag)) {
            actionFlag = "2B";
        } else if ("1C".equals(actionFlag)) {
            actionFlag = "2C";
        } else {
            throw new ServiceException("状态码不对");
        }
        return postingLogMapper.updateStatus(id, actionFlag);
    }

}
