package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdTraceEnt;
import com.datalink.fdop.engine.mapper.DwdTraceEntMapper;
import com.datalink.fdop.engine.service.DwdTraceEntService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdTraceEntServiceImpl extends ServiceImpl<DwdTraceEntMapper, DwdTraceEnt> implements DwdTraceEntService {

    @Autowired
    private DwdTraceEntMapper dwdTraceEntMapper;

    @Override
    public PageDataInfo<DwdTraceEnt> overview(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        Page<DwdTraceEnt> page = PageUtils.getPage(DwdTraceEnt.class);
        IPage<DwdTraceEnt> iPage = dwdTraceEntMapper.selectAll(plantId, dateFrom, dateTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdTraceEnt> selectNoPage(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        return dwdTraceEntMapper.selectNoPage(plantId, dateFrom, dateTo, sort, searchVo);
    }
}