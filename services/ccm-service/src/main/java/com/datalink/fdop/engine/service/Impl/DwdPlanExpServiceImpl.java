package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdPlanExp;
import com.datalink.fdop.engine.mapper.DwdPlanExpMapper;
import com.datalink.fdop.engine.service.DwdPlanExpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdPlanExpServiceImpl extends ServiceImpl<DwdPlanExpMapper, DwdPlanExp> implements DwdPlanExpService {

    @Autowired
    private DwdPlanExpMapper dwdPlanExpMapper;

    @Override
    public PageDataInfo<DwdPlanExp> overview(String verId, String companyId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        Page<DwdPlanExp> page = PageUtils.getPage(DwdPlanExp.class);
        IPage<DwdPlanExp> iPage = dwdPlanExpMapper.selectAll(verId, companyId, yearMonthFrom, yearMonthTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdPlanExp> selectNoPage(String verId, String companyId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwdPlanExpMapper.selectNoPage(verId, companyId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }
}