package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewSetLot;
import com.datalink.fdop.engine.mapper.DwsNewSetLotMapper;
import com.datalink.fdop.engine.service.DwsNewSetLotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewSetLotServiceImpl extends ServiceImpl<DwsNewSetLotMapper, DwsNewSetLot> implements DwsNewSetLotService {

    @Autowired
    private DwsNewSetLotMapper dwsNewSetLotMapper;

    @Override
    public PageDataInfo<DwsNewSetLot> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwsNewSetLot> page = PageUtils.getPage(DwsNewSetLot.class);
        IPage<DwsNewSetLot> iPage = dwsNewSetLotMapper.selectAll(yearMonth, verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewSetLot> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        return dwsNewSetLotMapper.selectNoPage(yearMonth, verId, plantId, sort, searchVo);
    }
}