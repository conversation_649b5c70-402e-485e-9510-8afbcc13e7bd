package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalFlow;
import com.datalink.fdop.engine.mapper.DwhFinalFlowMapper;
import com.datalink.fdop.engine.service.DwhFinalFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalFlowServiceImpl extends ServiceImpl<DwhFinalFlowMapper, DwhFinalFlow> implements DwhFinalFlowService {

    @Autowired
    private DwhFinalFlowMapper dwhFinalFlowMapper;

    @Override
    public PageDataInfo<DwhFinalFlow> overview(String verId, String  plantId, String productId, String sort, SearchVo searchVo) {
        Page<DwhFinalFlow> page = PageUtils.getPage(DwhFinalFlow.class);
        IPage<DwhFinalFlow> iPage = dwhFinalFlowMapper.selectAll(verId, plantId, productId,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalFlow> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        return dwhFinalFlowMapper.selectNoPage(verId, plantId, productId, sort, searchVo);
    }


}
