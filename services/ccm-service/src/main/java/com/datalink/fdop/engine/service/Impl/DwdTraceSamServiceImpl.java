package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdTraceSam;
import com.datalink.fdop.engine.mapper.DwdTraceSamMapper;
import com.datalink.fdop.engine.service.DwdTraceSamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdTraceSamServiceImpl extends ServiceImpl<DwdTraceSamMapper, DwdTraceSam> implements DwdTraceSamService {

    @Autowired
    private DwdTraceSamMapper dwdTraceSamMapper;

    @Override
    public PageDataInfo<DwdTraceSam> overview(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        Page<DwdTraceSam> page = PageUtils.getPage(DwdTraceSam.class);
        IPage<DwdTraceSam> iPage = dwdTraceSamMapper.selectAll(plantId, dateFrom, dateTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdTraceSam> selectNoPage(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        return dwdTraceSamMapper.selectNoPage(plantId, dateFrom, dateTo, sort, searchVo);
    }
}