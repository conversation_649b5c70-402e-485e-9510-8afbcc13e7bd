package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdTraceWip;
import com.datalink.fdop.engine.mapper.DwdTraceWipMapper;
import com.datalink.fdop.engine.service.DwdTraceWipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdTraceWipServiceImpl extends ServiceImpl<DwdTraceWipMapper, DwdTraceWip> implements DwdTraceWipService {

    @Autowired
    private DwdTraceWipMapper dwdTraceWipMapper;

    @Override
    public PageDataInfo<DwdTraceWip> overview(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        Page<DwdTraceWip> page = PageUtils.getPage(DwdTraceWip.class);
        IPage<DwdTraceWip> iPage = dwdTraceWipMapper.selectAll(plantId, dateFrom, dateTo, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdTraceWip> selectNoPage(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo) {
        return dwdTraceWipMapper.selectNoPage(plantId, dateFrom, dateTo, sort, searchVo);
    }
}