package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwdTraceMat;
import com.datalink.fdop.engine.mapper.DwdTraceMatMapper;
import com.datalink.fdop.engine.service.DwdTraceMatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwdTraceMatServiceImpl extends ServiceImpl<DwdTraceMatMapper, DwdTraceMat> implements DwdTraceMatService {

    @Autowired
    private DwdTraceMatMapper dwdTraceMatMapper;

    @Override
    public PageDataInfo<DwdTraceMat> overview(String yearMonth, String controlAreaId, String companyId,String sort, SearchVo searchVo) {
        Page<DwdTraceMat> page = PageUtils.getPage(DwdTraceMat.class);
        IPage<DwdTraceMat> iPage = dwdTraceMatMapper.selectAll(yearMonth, controlAreaId, companyId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwdTraceMat> selectNoPage(String yearMonth, String controlAreaId, String companyId, String sort, SearchVo searchVo) {
        return dwdTraceMatMapper.selectNoPage(yearMonth, controlAreaId, companyId, sort, searchVo);
    }
}