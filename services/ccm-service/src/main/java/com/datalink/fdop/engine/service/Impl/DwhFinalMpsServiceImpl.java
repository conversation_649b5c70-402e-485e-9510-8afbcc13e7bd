package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalMps;
import com.datalink.fdop.engine.mapper.DwhFinalMpsMapper;
import com.datalink.fdop.engine.service.DwhFinalMpsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalMpsServiceImpl extends ServiceImpl<DwhFinalMpsMapper, DwhFinalMps> implements DwhFinalMpsService {

    @Autowired
    private DwhFinalMpsMapper dwhFinalMpsMapper;

    @Override
    public PageDataInfo<DwhFinalMps> overview(
            String verId, String plantId, String yearMonthFrom,String yearMonthTo,
            String sort, SearchVo searchVo) {
        Page<DwhFinalMps> page = PageUtils.getPage(DwhFinalMps.class);
        IPage<DwhFinalMps> iPage = dwhFinalMpsMapper.selectAll(verId, plantId, yearMonthFrom, yearMonthTo,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalMps> selectNoPage(String verId, String plantId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwhFinalMpsMapper.selectNoPage(verId, plantId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }


}
