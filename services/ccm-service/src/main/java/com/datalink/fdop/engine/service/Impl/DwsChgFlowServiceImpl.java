package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsChgFlow;
import com.datalink.fdop.engine.mapper.DwsChgFlowMapper;
import com.datalink.fdop.engine.service.DwsChgFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsChgFlowServiceImpl extends ServiceImpl<DwsChgFlowMapper, DwsChgFlow> implements DwsChgFlowService {

    @Autowired
    private DwsChgFlowMapper dwsChgFlowMapper;

    @Override
    public PageDataInfo<DwsChgFlow> overview(String verId,String  plantId,String productId,String sort, SearchVo searchVo) {
        Page<DwsChgFlow> page = PageUtils.getPage(DwsChgFlow.class);
        IPage<DwsChgFlow> iPage = dwsChgFlowMapper.selectAll(verId,  plantId, productId,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsChgFlow> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        return dwsChgFlowMapper.selectNoPage(verId, plantId, productId, sort, searchVo);
    }


}
