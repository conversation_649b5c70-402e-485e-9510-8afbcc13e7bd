package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.base.api.domain.DwsFlowNewFlow;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.mapper.DwsFlowNewFlowMapper;
import com.datalink.fdop.engine.service.DwsFlowNewFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsFlowNewFlowServiceImpl extends ServiceImpl<DwsFlowNewFlowMapper, DwsFlowNewFlow> implements DwsFlowNewFlowService {

    @Autowired
    private DwsFlowNewFlowMapper dwsFlowNewFlowMapper;

    @Override
    public PageDataInfo<DwsFlowNewFlow> overview(String verId,String plantId,String productId,String sort, SearchVo searchVo) {
        Page<DwsFlowNewFlow> page = PageUtils.getPage(DwsFlowNewFlow.class);
        IPage<DwsFlowNewFlow> iPage = dwsFlowNewFlowMapper.selectAll(verId,plantId, productId,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsFlowNewFlow> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        return dwsFlowNewFlowMapper.selectNoPage(verId, plantId, productId, sort, searchVo);
    }

}
