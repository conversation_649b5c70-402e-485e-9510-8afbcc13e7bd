package com.datalink.fdop.engine.service.Impl;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.TechCompare;
import com.datalink.fdop.engine.mapper.TechCompareMapper;
import com.datalink.fdop.engine.service.TechCompareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-15 16:47
 */
@Service
public class TechCompareServiceImpl implements TechCompareService {

    @Autowired
    private TechCompareMapper techCompareMapper;

    @Override
    public PageDataInfo<TechCompare> query(String factoryId, String productId, String startTime, String endTime, String sort, SearchVo searchVo) {
        if (!techCompareMapper.selectCount().equals(0L)) {
            techCompareMapper.updateStatus();
        }
        List<TechCompare> techCompareList = techCompareMapper.query(factoryId, productId, startTime, endTime, sort, searchVo);
        return PageUtils.getPageInfo(techCompareList, techCompareList.size());
    }
}
