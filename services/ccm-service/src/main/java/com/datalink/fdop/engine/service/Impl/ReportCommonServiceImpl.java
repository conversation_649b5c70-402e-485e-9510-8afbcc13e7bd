package com.datalink.fdop.engine.service.Impl;

import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.engine.mapper.ReportCommonMapper;
import com.datalink.fdop.engine.service.ReportCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class ReportCommonServiceImpl implements ReportCommonService {

    @Autowired
    private ReportCommonMapper reportCommonMapper;
    @Override
    public List<String> listCostCenterType(String companyId, String yearMonthFrom, String yearMonthTo) {
        return reportCommonMapper.listCostCenterType(companyId,yearMonthFrom,yearMonthFrom);
    }

    @Override
    public List<SelectVo> listCostCenterId(String companyId, String yearMonthFrom, String yearMonthTo) {
        return reportCommonMapper.listCostCenterId(companyId, yearMonthFrom, yearMonthTo);
    }

    @Override
    public List<SelectVo> listCostElementId() {
        return reportCommonMapper.listCostElementId();
    }

    @Override
    public List<SelectVo> listCostStructureId() {
        return reportCommonMapper.listCostStructureId();
    }

    @Override
    public List<String> listWorkArea() {
        return reportCommonMapper.listWorkArea();
    }

    @Override
    public List<SelectVo> listEquipGroupId() {
        return reportCommonMapper.listEquipGroupId();
    }

    @Override
    public List<String> listLotCategory() {
        return reportCommonMapper.listLotCategory();
    }

    @Override
    public List<String> listLotType() {
        return reportCommonMapper.listLotType();
    }
}
