package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.ActualPosted;
import com.datalink.fdop.engine.mapper.ActualPostedMapper;
import com.datalink.fdop.engine.service.ActualPostedService;
import com.datalink.fdop.engine.utils.TrinoSqlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-07 15:33
 */
@Service
public class ActualPostedServiceImpl extends ServiceImpl<ActualPostedMapper, ActualPosted> implements ActualPostedService {

    @Autowired
    private ActualPostedMapper actualPostedMapper;

    @Override
    public PageDataInfo<ActualPosted> getOrg(String sort, SearchVo searchVo) {
        List<ActualPosted> iPage = actualPostedMapper.getOrg(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public PageDataInfo<ActualPosted> getEdit(String sort, SearchVo searchVo) {
        List<ActualPosted> iPage = actualPostedMapper.getEdit(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public PageDataInfo<ActualPosted> getAll(String sort, SearchVo searchVo) {
        List<ActualPosted> iPage = actualPostedMapper.getAll(sort, searchVo);
        return PageUtils.getPageInfo(iPage, iPage.size());
    }

    @Override
    public int saveData(ActualPosted actualPosted) {
        if (actualPostedMapper.selectByKey(actualPosted) == null) {
            actualPostedMapper.add(TrinoSqlUtils.actualPostedSql(actualPosted));
        } else {
            actualPostedMapper.updateByKey(actualPosted);
        }
        return 1;
    }

    @Override
    public int deleteByList(List<ActualPosted> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("请指定要删除的目标");
        }
        int i = 0;
        for (ActualPosted actualPosted : list) {
            i = i + actualPostedMapper.deleteByKey(actualPosted);
        }
        return i;
    }

}
