package com.datalink.fdop.engine.service.Impl;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.engine.api.domain.*;
import com.datalink.fdop.engine.mapper.CostOutMapper;
import com.datalink.fdop.engine.model.vo.WipCostReportVo;
import com.datalink.fdop.engine.service.CostOutService;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-03-06 15:53
 */
@Service
public class CostOutServiceImpl implements CostOutService {

    @Autowired
    private CostOutMapper costOutMapper;

    @Override
    public Map<String, Object> getWipOrInv(String verId, String companyId, String factoryId, Long yearMonth, String costType, SearchVo searchVo, String prefix) {
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> dynamicColumns = Maps.newLinkedHashMap();
        List<WipCostReportVo> wipCostReportVoList = costOutMapper.getWipOrInv(verId, companyId, factoryId, yearMonth, searchVo, prefix, costType);

        if (wipCostReportVoList.size() != 0) {
            for (WipCostReportVo wipCostReportVo : wipCostReportVoList) {
                List<WipCostReport200> wipCostReport200List = costOutMapper.selectWipOrInvItem(verId, companyId, factoryId, yearMonth, wipCostReportVo.getProductId(), costType, prefix);
                if (wipCostReport200List.size() != 0) {
                    for (WipCostReport200 wipCostReport200 : wipCostReport200List) {
                        wipCostReportVo.getCostStructure().put(wipCostReport200.getCostStructureDesc(), wipCostReport200.getCost());
                        if (!dynamicColumns.containsKey(wipCostReport200.getCostStructureDesc())) {
                            dynamicColumns.put(wipCostReport200.getCostStructureDesc(), wipCostReport200.getCostStructureDesc());
                        }
                    }
                }
            }
        }
        result.put("data", PageUtils.getPageInfo(wipCostReportVoList, wipCostReportVoList.size()));
        result.put("dynamicColumns", dynamicColumns);
        return result;
    }

    @Override
    public Map<String, Object> getRd(String verId, String companyId, String factoryId, String costCenterId, String wbsId, Long yearMonth, SearchVo searchVo) {
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> dynamicColumns = Maps.newLinkedHashMap();
        List<RdCostReport100> rdCostReport100List = costOutMapper.getRd(verId, companyId, factoryId, costCenterId, wbsId, yearMonth, searchVo);
        if (rdCostReport100List.size() != 0) {
            for (RdCostReport100 rdCostReport100 : rdCostReport100List) {
                List<RdCostReport200> rdCostReport200List = costOutMapper.getRdItem(verId, companyId, factoryId, costCenterId, wbsId, yearMonth, rdCostReport100.getProductId(), rdCostReport100.getLotType());
                if (rdCostReport200List.size() != 0) {
                    for (RdCostReport200 rdCostReport200 : rdCostReport200List) {
                        rdCostReport100.getCostStructure().put(rdCostReport200.getCostStructureDesc(), rdCostReport200.getCost());
                        if (!dynamicColumns.containsKey(rdCostReport200.getCostStructureDesc())) {
                            dynamicColumns.put(rdCostReport200.getCostStructureDesc(), rdCostReport200.getCostStructureDesc());
                        }
                    }
                }
            }
        }
        result.put("data", PageUtils.getPageInfo(rdCostReport100List, rdCostReport100List.size()));
        result.put("dynamicColumns", dynamicColumns);
        return result;
    }

    @Override
    public Map<String, Object> getEvent(String verId, String companyId, String factoryId, String eventCode, Long yearMonth, SearchVo searchVo) {
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> dynamicColumns = Maps.newLinkedHashMap();
        List<EventCostReport100> eventCostReport100List = costOutMapper.getEvent(verId, companyId, factoryId, eventCode, yearMonth, searchVo);
        if (eventCostReport100List.size() != 0) {
            for (EventCostReport100 eventCostReport100 : eventCostReport100List) {
                List<EventCostReport200> eventCostReport200List = costOutMapper.getEventItem(verId, companyId, factoryId, eventCode, yearMonth, eventCostReport100.getProductId());
                if (eventCostReport200List.size() != 0) {
                    for (EventCostReport200 eventCostReport200 : eventCostReport200List) {
                        eventCostReport100.getCostStructure().put(eventCostReport200.getCostStructureDesc(), eventCostReport200.getCost());
                        if (!dynamicColumns.containsKey(eventCostReport200.getCostStructureDesc())) {
                            dynamicColumns.put(eventCostReport200.getCostStructureDesc(), eventCostReport200.getCostStructureDesc());
                        }
                    }
                }
            }
        }
        result.put("data", PageUtils.getPageInfo(eventCostReport100List, eventCostReport100List.size()));
        result.put("dynamicColumns", dynamicColumns);
        return result;
    }

    @Override
    public Map<String, Object> getPc(String verId, String companyId, String factoryId, String workOrder, Long yearMonth, SearchVo searchVo) {
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> dynamicColumns = Maps.newLinkedHashMap();
        List<PcCostReport100> pcCostReport100List = costOutMapper.getPc(verId, companyId, factoryId, workOrder, yearMonth, searchVo);
        if (pcCostReport100List.size() != 0) {
            for (PcCostReport100 pcCostReport100 : pcCostReport100List) {
                List<PcCostReport200> pcCostReport200List = costOutMapper.getPcItem(verId, companyId, factoryId, workOrder, yearMonth);
                if (pcCostReport200List.size() != 0) {
                    for (PcCostReport200 pcCostReport200 : pcCostReport200List) {
                        pcCostReport100.getCostStructure().put(pcCostReport200.getCostStructureDesc(), pcCostReport200.getCost());
                        if (!dynamicColumns.containsKey(pcCostReport200.getCostStructureDesc())) {
                            dynamicColumns.put(pcCostReport200.getCostStructureDesc(), pcCostReport200.getCostStructureDesc());
                        }
                    }
                }
            }
        }
        result.put("data", PageUtils.getPageInfo(pcCostReport100List, pcCostReport100List.size()));
        result.put("dynamicColumns", dynamicColumns);
        return result;
    }
}
