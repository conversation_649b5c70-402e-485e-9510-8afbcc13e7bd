package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewSetOrd;
import com.datalink.fdop.engine.mapper.DwsNewSetOrdMapper;
import com.datalink.fdop.engine.service.DwsNewSetOrdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwsNewSetOrdServiceImpl extends ServiceImpl<DwsNewSetOrdMapper, DwsNewSetOrd> implements DwsNewSetOrdService {

    @Autowired
    private DwsNewSetOrdMapper dwsNewSetOrdMapper;

    @Override
    public PageDataInfo<DwsNewSetOrd> overview(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        Page<DwsNewSetOrd> page = PageUtils.getPage(DwsNewSetOrd.class);
        IPage<DwsNewSetOrd> iPage = dwsNewSetOrdMapper.selectAll(yearMonth, verId, plantId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwsNewSetOrd> selectNoPage(String yearMonth, String verId, String plantId, String sort, SearchVo searchVo) {
        return dwsNewSetOrdMapper.selectNoPage(yearMonth, verId, plantId, sort, searchVo);
    }
}