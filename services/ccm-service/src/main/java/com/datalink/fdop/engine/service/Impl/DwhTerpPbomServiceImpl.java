package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhTerpPbom;
import com.datalink.fdop.engine.mapper.DwhTerpPbomMapper;
import com.datalink.fdop.engine.service.DwhTerpPbomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhTerpPbomServiceImpl extends ServiceImpl<DwhTerpPbomMapper, DwhTerpPbom> implements DwhTerpPbomService {

    @Autowired
    private DwhTerpPbomMapper dwhTerpPbomMapper;

    @Override
    public PageDataInfo<DwhTerpPbom> overview(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        Page<DwhTerpPbom> page = PageUtils.getPage(DwhTerpPbom.class);
        IPage<DwhTerpPbom> iPage = dwhTerpPbomMapper.selectAll(verId, plantId, productId, page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhTerpPbom> listAll() {
        return dwhTerpPbomMapper.listAll();
    }

    @Override
    public List<DwhTerpPbom> selectNoPage(String verId, String plantId, String productId, String sort, SearchVo searchVo) {
        return dwhTerpPbomMapper.selectNoPage(verId, plantId, productId, sort, searchVo);
    }


}
